
10:46:45.566 [info] Loading 161 CA(s) from :otp store

10:46:45.733 id=0 [debug] handled request client -> server initialize in 110ms

10:46:45.743 [debug] sent notification server -> client window/logMessage

10:46:45.746 id=9 [debug] sent request server -> client client/registerCapability

10:46:45.746 [debug] NextLS v0.23.3 has initialized!

10:46:45.747 [debug] sent notification server -> client window/logMessage

10:46:45.747 [debug] Log file located at /Users/<USER>/Developer/OSS/instructor_ex/.elixir-tools/next-ls.log

10:46:45.747 [debug] sent notification server -> client window/logMessage

10:46:45.747 [info] [extension] Credo initializing with options %NextLS.InitOpts.Extensions.Credo{enable: true, cli_options: []}
