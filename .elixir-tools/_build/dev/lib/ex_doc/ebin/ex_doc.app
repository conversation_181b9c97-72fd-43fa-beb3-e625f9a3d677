{application,ex_doc,
             [{modules,['Elixir.ExDoc','Elixir.ExDoc.Application',
                        'Elixir.ExDoc.Autolink','Elixir.ExDoc.CLI',
                        'Elixir.ExDoc.Config','Elixir.ExDoc.DocAST',
                        'Elixir.ExDoc.Formatter.EPUB',
                        'Elixir.ExDoc.Formatter.EPUB.Assets',
                        'Elixir.ExDoc.Formatter.EPUB.Templates',
                        'Elixir.ExDoc.Formatter.HTML',
                        'Elixir.ExDoc.Formatter.HTML.Assets',
                        'Elixir.ExDoc.Formatter.HTML.SearchData',
                        'Elixir.ExDoc.Formatter.HTML.Templates',
                        'Elixir.ExDoc.FunctionNode',
                        'Elixir.ExDoc.GroupMatcher','Elixir.ExDoc.Language',
                        'Elixir.ExDoc.Language.Elixir',
                        'Elixir.ExDoc.Language.Erlang',
                        'Elixir.ExDoc.Markdown',
                        'Elixir.ExDoc.Markdown.Earmark',
                        'Elixir.ExDoc.ModuleNode','Elixir.ExDoc.Refs',
                        'Elixir.ExDoc.Retriever',
                        'Elixir.ExDoc.Retriever.Error',
                        'Elixir.ExDoc.ShellLexer','Elixir.ExDoc.TypeNode',
                        'Elixir.ExDoc.Utils','Elixir.Mix.Tasks.Docs']},
              {optional_applications,[makeup_c]},
              {applications,[kernel,stdlib,elixir,eex,earmark_parser,
                             makeup_elixir,makeup_erlang,makeup_c]},
              {description,"ExDoc is a documentation generation tool for Elixir"},
              {registered,[]},
              {vsn,"0.31.0"},
              {mod,{'Elixir.ExDoc.Application',[]}}]}.
