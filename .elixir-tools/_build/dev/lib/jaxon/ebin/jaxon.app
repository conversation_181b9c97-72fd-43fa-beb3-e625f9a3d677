{application,jaxon,
             [{modules,['Elixir.Jaxon','Elixir.Jaxon.Decoders.Query',
                        'Elixir.Jaxon.Decoders.Value',
                        'Elixir.Jaxon.Decoders.Values',
                        'Elixir.Jaxon.EncodeError','Elixir.Jaxon.Event',
                        'Elixir.Jaxon.ParseError','Elixir.Jaxon.Parser',
                        'Elixir.Jaxon.Parsers.NifParser','Elixir.Jaxon.Path',
                        'Elixir.Jaxon.Stream']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"Jaxon is an efficient and simple event-based JSON parser for Elixir, it's main goal is to be able to parse huge JSON files with minimal memory footprint."},
              {registered,[]},
              {vsn,"2.0.8"}]}.
