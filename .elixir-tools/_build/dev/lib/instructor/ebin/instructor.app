{application,instructor,
             [{modules,['Elixir.Instructor','Elixir.Instructor.Adapter',
                        'Elixir.Instructor.Adapters.Anthropic',
                        'Elixir.Instructor.Adapters.Gemini',
                        'Elixir.Instructor.Adapters.Groq',
                        'Elixir.Instructor.Adapters.Llamacpp',
                        'Elixir.Instructor.Adapters.Ollama',
                        'Elixir.Instructor.Adapters.OpenAI',
                        'Elixir.Instructor.Adapters.VLLM',
                        'Elixir.Instructor.Adapters.XAI',
                        'Elixir.Instructor.EctoType',
                        'Elixir.Instructor.ErrorFormatter',
                        'Elixir.Instructor.Extras.ChainOfThought',
                        'Elixir.Instructor.Extras.ChainOfThought.ReasoningStep',
                        'Elixir.Instructor.JSONSchema',
                        'Elixir.Instructor.JSONStreamParser',
                        'Elixir.Instructor.SSEStreamParser',
                        'Elixir.Instructor.Types.Duration',
                        'Elixir.Instructor.Validator']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,ecto,jason,req,
                             jaxon]},
              {description,"Structured prompting for OpenAI and OSS LLMs"},
              {registered,[]},
              {vsn,"0.1.0"}]}.
