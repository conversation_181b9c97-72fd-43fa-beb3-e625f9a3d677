{application,elixir_make,
             [{modules,['Elixir.ElixirMake.Artefact',
                        'Elixir.ElixirMake.Compiler',
                        'Elixir.ElixirMake.Precompiler',
                        'Elixir.Mix.Tasks.Compile.ElixirMake',
                        'Elixir.Mix.Tasks.ElixirMake.Checksum',
                        'Elixir.Mix.Tasks.ElixirMake.Precompile']},
              {optional_applications,[castore]},
              {applications,[kernel,stdlib,elixir,logger,castore]},
              {description,"A Make compiler for Mix"},
              {registered,[]},
              {vsn,"0.7.7"}]}.
